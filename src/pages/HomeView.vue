<script setup lang="ts" name="HomeView">
import { toRefs } from 'vue'
import { useRoute } from 'vue-router'
let route = useRoute()
let { query } = toRefs(route)
</script>

<template>
  <div class="home">
    <div class="title-tips">
      <div class="title-tips-left">
        <span class="title"> Not Available in Your Country </span>
        <div class="ip-country">
          Your IP <span class="ip">{{ query.ip }}</span> {{ query.country }}
        </div>
        <div class="ip-country-bottom">
          Your IP address indicates that you're attempting to access our services from a restricted
          jurisdiction.
        </div>
      </div>

      <img src="/src/assets/img/tips.png" style="width: 63px; height: 63px" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.home {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  .title-tips {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    .title {
      color: #3f62a8;
      font-size: 23px;
      font-weight: bold;
    }
    .ip-country {
      color: #999999;
      font-size: 14px;
      font-weight: bold;
      margin-top: 50px;
      .ip {
        color: #666666;
        font-size: 14px;
        font-weight: bold;
      }
    }

    .ip-country-bottom {
      color: #999999;
      font-size: 14px;
      font-weight: bold;
    }

    img {
      align-self: flex-start;
      margin-top: 10px;
    }
  }
}
</style>
