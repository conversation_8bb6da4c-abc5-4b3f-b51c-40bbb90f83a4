<script setup lang="ts" name="LandingView">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute();

// 去落地页面
const goLanding = () => {
  let {gameurl} = route.query;
  let adjustUrl = gameurl as string;
  adjustUrl = decodeURIComponent(adjustUrl);
    // 创建一个新的 iframe 元素
  const iframe = document.createElement('iframe');
  iframe.id = 'landing-iframe';
  iframe.width = '100%';
  iframe.height = '100%';
  iframe.frameBorder = '0';
  iframe.src = adjustUrl;

  // 将 iframe 添加到 DOM 中
  const container = document.getElementById('landingiframe')
  console.log('container ===>', container, adjustUrl);
  if (container) {
    container.appendChild(iframe);
  }
}

onMounted(() => {
  setTimeout(() => {
    goLanding();
  }, 110);
  
})
</script>

<template>
  <div class="landing" id="landingiframe" @click="goLanding">
  <!-- <div class="test"> >>>> {{ m_userAgent }}</div>
  <div class="test"> >>>> {{ m_brand }}</div> -->
    <!-- <div class="landing-content">
      <div class="top-man">
        <img src="/src/assets/img/land/top_man.png" />
      </div>
      <div class="top-logo">
        <img src="/src/assets/img/land/land_logo.png" />
      </div>
      <div class="go-landing">
        <img src="/src/assets/img/land/get_button.png" />
      </div>
      <div class="tips-content">
        <img src="/src/assets/img/land/tips_1.png" />
        <img src="/src/assets/img/land/tips_2.png" />
        <img src="/src/assets/img/land/tips_3.png" />
      </div>
    </div>

    <div class="bottom-tips">www.playtime.ph</div> -->
  </div>
</template>

<style lang="scss" scoped>
.landing {
  overflow: scroll;
  // background-image: url('@/assets/img/land/bg.png');
  // background-size: 100% 100%;
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .test {
    font-size: large;
    font-weight: bold;
    color: #000000;
  }
  .landing-content {
    position: relative;
    .top-man {
      img {
        width: 100%;
        height: auto;
        object-fit: contain;
      }
    }
    .top-logo {
      background-color: rgba($color: #000000, $alpha: 0.8);
      text-align: center;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      img {
        width: 100px;
        padding-top: 10px;
        padding-bottom: 10px;
      }
    }
    .go-landing {
      text-align: center;
      margin-top: -65px;
      img {
        width: 80%;
      }
    }
    .tips-content {
      width: 100%;
      text-align: center;
      margin-top: 10px;
      margin-bottom: 50px;
      img {
        width: 90%;
        margin-top: 15px;
      }
    }
  }

  .bottom-tips {
    background-color: rgba($color: #000000, $alpha: 0.8);
    color: #ffffff;
    font-size: 15px;
    font-weight: bold;
    text-align: center;
    height: 40px;
    line-height: 40px;
  }
}
</style>
